import 'package:flutter/material.dart';
import 'design_system.dart';
import 'app_typography.dart';

/// Centralized Typography System for HSK Screens
///
/// This class provides a consistent typography system across all HSK learning screens,
/// using the comprehensive AppTypography system with NotoSansSC for optimal Chinese
/// character rendering and modern design.
///
/// Benefits:
/// - ✨ Consistency: Unified with app-wide typography system
/// - 🔧 Maintainability: Centralized typography management
/// - 📱 Performance: Optimized Chinese character rendering
/// - 🌐 Accessibility: Better Unicode support with responsive scaling
/// - 🎨 Professional: Modern, clean typography with proper hierarchy
///
/// @deprecated Use AppTypography.getHSKStyles(context) for new implementations
class HskTypography {
  // Private constructor to prevent instantiation
  HskTypography._();

  /// Base font family for all HSK screens
  /// @deprecated Use AppTypography.sansSerifFont instead
  static const String _fontFamily = AppTypography.sansSerifFont;

  /// Font weight for all text (Bold for better readability)
  /// @deprecated Use AppTypography.bold instead
  static const FontWeight _fontWeight = AppTypography.bold;

  // =====================================================
  // APP TITLE & HEADERS
  // =====================================================

  /// Main app title (e.g., "CHINESE IN FLOW")
  static const TextStyle appTitle = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizeHeadingL,
    letterSpacing: 1.2,
    color: Colors.white,
    shadows: [
      Shadow(
        offset: Offset(1, 1),
        blurRadius: DesignSystem.elevationS + 1.0,
        color: Color.fromARGB(150, 0, 0, 0),
      ),
    ],
  );

  /// Screen titles in app bars
  static const TextStyle screenTitle = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizeXXL,
    color: Colors.white,
  );

  /// Stage/progress indicators
  static const TextStyle stageIndicator = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizeL,
    color: Colors.white,
  );

  // =====================================================
  // CHINESE CHARACTER DISPLAY
  // =====================================================

  /// Large Chinese characters (main display)
  static const TextStyle chineseCharacterLarge = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizeChineseL,
    color: Colors.white,
  );

  /// Medium Chinese characters (buttons, smaller displays)
  static const TextStyle chineseCharacterMedium = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizeChineseM,
    color: Colors.white,
  );

  /// Small Chinese characters (compact displays)
  static const TextStyle chineseCharacterSmall = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizeHeadingM,
    color: Colors.white,
  );

  // =====================================================
  // PINYIN & ENGLISH TEXT
  // =====================================================

  /// Pinyin text (medium size) - use with theme colors
  static const TextStyle pinyinMedium = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizePinyinL,
    // Color should be applied via Theme.of(context).colorScheme.onPrimary
  );

  /// Pinyin text (small size) - use with theme colors
  static const TextStyle pinyinSmall = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: FontWeight.normal, // Normal weight for pinyin readability
    fontSize: DesignSystem.fontSizePinyinM,
    // Color should be applied via Theme.of(context).colorScheme.onPrimary
  );

  /// English translation (medium) - use with theme colors
  static const TextStyle englishMedium = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizeXXL,
    // Color should be applied via Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.7)
  );

  /// English translation (small) - use with theme colors
  static const TextStyle englishSmall = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizeM,
    // Color should be applied via Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.7)
  );

  // =====================================================
  // BUTTONS & INTERACTIVE ELEMENTS
  // =====================================================

  /// Button text (primary actions) - use with theme colors
  static const TextStyle buttonPrimary = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizeXL,
    // Color should be applied via Theme.of(context).colorScheme.onPrimary
  );

  /// Button text (secondary actions) - use with theme colors
  static const TextStyle buttonSecondary = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizeL,
    // Color should be applied via Theme.of(context).colorScheme.onPrimary
  );

  /// Small button labels
  static const TextStyle buttonLabel = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: FontWeight.normal,
    fontSize: DesignSystem.fontSizeS,
    color: Colors.grey,
  );

  // =====================================================
  // FEEDBACK & STATUS MESSAGES
  // =====================================================

  /// Success messages (e.g., "Correct!", "Well Done!")
  static const TextStyle feedbackSuccess = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizePinyinL,
    color: Colors.green,
  );

  /// Error messages (e.g., "Time over", "Sudden Death!")
  static const TextStyle feedbackError = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizePinyinL,
    color: Colors.red,
  );

  /// Congratulations messages
  static const TextStyle congratulations = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizeHeadingS,
    color: Colors.white,
  );

  /// End session messages
  static const TextStyle endSession = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizeChineseM,
    color: Colors.white,
    shadows: [
      Shadow(
        offset: Offset(2.0, 2.0),
        blurRadius: DesignSystem.elevationS + 1.0,
        color: Color.fromARGB(128, 0, 0, 0),
      ),
    ],
  );

  // =====================================================
  // COMPLETION SCREENS
  // =====================================================

  /// Completion screen title
  static const TextStyle completionTitle = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizeDisplayM,
    color: Colors.white,
    shadows: [
      Shadow(
        offset: Offset(1.0, 1.0),
        blurRadius: DesignSystem.elevationS,
        color: Color.fromARGB(100, 0, 0, 0),
      ),
    ],
  );

  /// Completion screen statistics
  static const TextStyle completionStats = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizeHeadingL,
    color: Colors.white,
  );

  /// Completion screen description
  static const TextStyle completionDescription = TextStyle(
    fontFamily: _fontFamily,
    fontWeight: _fontWeight,
    fontSize: DesignSystem.fontSizeXL,
    color: Colors.white,
  );

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  /// Creates a responsive text style based on screen size
  static TextStyle responsive(BuildContext context, TextStyle baseStyle) {
    final textScaler = MediaQuery.textScalerOf(context);
    final scaledFontSize = baseStyle.fontSize != null
        ? (baseStyle.fontSize! *
            textScaler.scale(baseStyle.fontSize!) /
            baseStyle.fontSize!)
        : null;

    return baseStyle.copyWith(
      fontSize: scaledFontSize?.clamp(
        (baseStyle.fontSize ?? 14.0) * 0.8,
        (baseStyle.fontSize ?? 14.0) * 1.3,
      ),
    );
  }

  /// Creates a text style with custom color while maintaining font family
  static TextStyle withColor(TextStyle baseStyle, Color color) {
    return baseStyle.copyWith(color: color);
  }

  /// Creates a text style with custom size while maintaining font family
  static TextStyle withSize(TextStyle baseStyle, double fontSize) {
    return baseStyle.copyWith(fontSize: fontSize);
  }

  // =====================================================
  // RESPONSIVE FONT SCALING METHODS
  // =====================================================

  /// Get responsive Chinese character font size with manufacturer adjustments
  /// Combines DesignSystem constants with context-aware scaling
  static double getChineseFontSize(BuildContext context, {String? text}) {
    return DesignSystem.getAdjustedFontSize(
      context,
      DesignSystem.fontSizeChineseM,
      text: text,
    );
  }

  /// Get responsive pinyin font size with manufacturer adjustments
  /// Optimized for pinyin readability across devices
  static double getPinyinFontSize(BuildContext context, {String? text}) {
    return DesignSystem.getAdjustedFontSize(
      context,
      DesignSystem.fontSizePinyinM,
      text: text,
    );
  }

  /// Get responsive English text font size with manufacturer adjustments
  /// Ensures proper contrast and readability for translations
  static double getEnglishFontSize(BuildContext context, {String? text}) {
    return DesignSystem.getAdjustedFontSize(
      context,
      DesignSystem.fontSizeM,
      text: text,
    );
  }

  /// Get responsive font weight with manufacturer adjustments
  /// Applies pixel-perfect font weight compensation
  static FontWeight getAdjustedFontWeight(
    FontWeight baseFontWeight, {
    String? text,
  }) {
    return DesignSystem.getAdjustedFontWeight(baseFontWeight, text: text);
  }

  /// Get responsive Chinese character style with full manufacturer adjustments
  /// Complete style with font size, weight, and manufacturer compensation
  static TextStyle getResponsiveChineseStyle(
    BuildContext context, {
    String? text,
    Color? color,
  }) {
    return chineseCharacterMedium.copyWith(
      fontSize: getChineseFontSize(context, text: text),
      fontWeight: getAdjustedFontWeight(
        chineseCharacterMedium.fontWeight ?? FontWeight.bold,
        text: text,
      ),
      color: color,
    );
  }

  /// Get responsive pinyin style with full manufacturer adjustments
  /// Complete style with font size, weight, and manufacturer compensation
  static TextStyle getResponsivePinyinStyle(
    BuildContext context, {
    String? text,
    Color? color,
  }) {
    return pinyinSmall.copyWith(
      fontSize: getPinyinFontSize(context, text: text),
      fontWeight: getAdjustedFontWeight(
        pinyinSmall.fontWeight ?? FontWeight.normal,
        text: text,
      ),
      color: color,
    );
  }

  /// Get responsive English style with full manufacturer adjustments
  /// Complete style with font size, weight, and manufacturer compensation
  static TextStyle getResponsiveEnglishStyle(
    BuildContext context, {
    String? text,
    Color? color,
  }) {
    return englishSmall.copyWith(
      fontSize: getEnglishFontSize(context, text: text),
      fontWeight: getAdjustedFontWeight(
        englishSmall.fontWeight ?? FontWeight.bold,
        text: text,
      ),
      color: color,
    );
  }
}
